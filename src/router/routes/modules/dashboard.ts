import type { AppRouteModule } from '@/router/types';
import { LAYOUT } from '@/router/constant';
import { t } from '@/hooks/web/useI18n';
// import { screenUrl } from '@/utils/screen';

const dashboard: AppRouteModule = {
  path: '/dashboard',
  name: 'Dashboard',
  component: LAYOUT,
  redirect: '/dashboard/analysis',
  meta: {
    orderNo: 10,
    icon: 'ion:grid-outline',
    title: t('routes.dashboard.dashboard'),
    hideChildrenInMenu: true,
    hideBreadcrumb: true,
  },
  children: [
    {
      path: 'analysis',
      name: 'Analysis',
      component: () => import('@/views/security/dashboard/glasses/index.vue'),
      meta: {
        affix: true,
        title: t('routes.dashboard.dashboard'),
      },
    },
    // {
    //   path: 'analysis',
    //   name: 'Analysis',
    //   component: () => import('@/layouts/local/dashboard/analysis/index.vue'),
    //   meta: {
    //     // affix: true,
    //     title: t('routes.dashboard.analysis'),
    //   },
    // },
    // {
    //   path: screenUrl,
    //   name: 'Workbench',
    //   component: () => import('@/layouts/local/dashboard/workbench/index.vue'),
    //   meta: {
    //     title: t('routes.dashboard.workbench'),
    //   },
    // },
    // {
    //   path: 'environmentControl',
    //   name: 'EnvironmentControl',
    //   component: () => import('@/layouts/local/dashboard/environmentControl/index.vue'),
    //   meta: {
    //     // affix: true,
    //     title: t('routes.dashboard.environmentControl'),
    //   },
    // },
  ],
};

export default dashboard;
