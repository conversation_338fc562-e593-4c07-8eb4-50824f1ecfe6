<template>
  <BasicModal
    v-bind="$attrs"
    title="AR眼镜使用记录详情"
    @register="registerInnerModal"
    :canFullscreen="false"
    :footer="null"
    width="800px"
  >
    <div class="p-4">
      <a-descriptions :column="2" bordered>
        <a-descriptions-item label="设备编号">
          {{ recordData.deviceNo }}
        </a-descriptions-item>
        <a-descriptions-item label="使用人员工号">
          {{ recordData.userId }}
        </a-descriptions-item>
        <a-descriptions-item label="使用人员名称">
          {{ recordData.userName }}
        </a-descriptions-item>
        <a-descriptions-item label="站点名称">
          {{ recordData.stationName }}
        </a-descriptions-item>
        <a-descriptions-item label="登录时间">
          {{ recordData.loginTime }}
        </a-descriptions-item>
        <a-descriptions-item label="登出时间">
          {{ recordData.logoutTime }}
        </a-descriptions-item>
        <a-descriptions-item label="使用时长">
          {{ recordData.totalDuration }}分钟
        </a-descriptions-item>
        <a-descriptions-item label="备注" :span="2">
          {{ recordData.remark || '无' }}
        </a-descriptions-item>
      </a-descriptions>
    </div>
  </BasicModal>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { Descriptions as ADescriptions, DescriptionsItem as ADescriptionsItem } from 'ant-design-vue';

  defineOptions({ name: 'GlassUsageLogInfoModal' });

  const recordData = ref<any>({});

  const [registerInnerModal] = useModalInner((data: any) => {
    recordData.value = data || {};
  });
</script>
