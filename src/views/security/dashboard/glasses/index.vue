<template>
  <PageWrapper dense>
    <!-- AR眼镜使用统计概览 -->
    <GlassesStatsOverview
      ref="statsOverviewRef"
      :search-params="searchParams"
    />

    <BasicTable @register="registerTable">
      <template #toolbar>
        <Space>
          <a-button
            class="<sm:hidden"
            @click="
              downloadExcel(glassUsageLogExport, 'AR眼镜使用台账', getForm().getFieldsValue())
            "
            >导出</a-button
          >
          <a-button
            class="<sm:hidden"
            type="primary"
            danger
            @click="multipleRemove(glassUsageLogRemove)"
            :disabled="!selected"
            >删除</a-button
          >
          <a-button type="primary" @click="handleAdd">新增</a-button>
        </Space>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            stopButtonPropagation
            :actions="[
              {
                label: '详情',
                icon: IconEnum.PREVIEW,
                type: 'primary',
                ghost: true,
                onClick: handleInfo.bind(null, record),
              },
              {
                label: '修改',
                icon: IconEnum.EDIT,
                type: 'primary',
                ghost: true,
                onClick: handleEdit.bind(null, record),
              },
              {
                label: '删除',
                icon: IconEnum.DELETE,
                type: 'primary',
                danger: true,
                ghost: true,
                popConfirm: {
                  placement: 'left',
                  title: `是否确认删除?`,
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <GlassUsageLogModal @register="registerModal" @reload="reloadData" />
    <GlassUsageLogInfoModal @register="registerInfoModal" />
  </PageWrapper>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { PageWrapper } from '@/components/Page';
  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import { Space } from 'ant-design-vue';
  import {
    glassUsageLogList,
    glassUsageLogExport,
    glassUsageLogRemove,
  } from '@/api/security/glassUsageLog';
  import GlassesStatsOverview from './GlassesStatsOverview.vue';
  import GlassUsageLogModal from './Modal.vue';
  import GlassUsageLogInfoModal from './InfoModal.vue';
  import { useModal } from '@/components/Modal';
  import { downloadExcel } from '@/utils/file/download';
  import { formSchemas, columns } from './data';
  import { IconEnum } from '@/enums/appEnum';

  defineOptions({ name: 'GlassUsageLog' });

  const statsOverviewRef = ref();
  const searchParams = ref({});

  const [registerTable, { reload, multipleRemove, selected, getForm }] = useTable({
    rowSelection: {
      type: 'checkbox',
    },
    title: 'AR眼镜使用台账',
    showIndexColumn: false,
    api: glassUsageLogList,
    rowKey: 'id',
    useSearchForm: true,
    beforeFetch: (params) => {
      // 更新查询参数，触发统计组件更新
      searchParams.value = { ...params };
      return params;
    },
    formConfig: {
      schemas: formSchemas,
      labelWidth: 120,
      name: 'glassUsageLog',
      baseColProps: {
        xs: 24,
        sm: 24,
        md: 24,
        lg: 6,
      },
      // 日期选择格式化
      fieldMapToTime: [
        [
          'loginTime',
          ['params[loginTimeBegin]', 'params[loginTimeEnd]'],
          ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59'],
        ],
        [
          'logoutTime',
          ['params[logoutTimeBegin]', 'params[logoutTimeEnd]'],
          ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59'],
        ],
      ],
    },
    columns: columns,
    actionColumn: {
      width: 240,
      title: '操作',
      key: 'action',
      fixed: 'right',
    },
  });

  const [registerModal, { openModal }] = useModal();
  const [registerInfoModal, { openModal: openInfoModal }] = useModal();

  // 刷新表格和统计数据
  async function reloadData() {
    await reload();
    if (statsOverviewRef.value?.refresh) {
      await statsOverviewRef.value.refresh(searchParams.value);
    }
  }

  function handleEdit(record: Recordable) {
    openModal(true, { record, update: true });
  }

  function handleAdd() {
    openModal(true, { update: false });
  }

  function handleInfo(record: Recordable) {
    openInfoModal(true, record);
  }

  async function handleDelete(record: Recordable) {
    const { id } = record;
    await glassUsageLogRemove([id]);
    await reloadData();
  }
</script>

<style scoped></style>
