---
title: 国铁眼镜
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 国铁眼镜

Base URLs:

# Authentication

# 国铁/AR 眼镜使用信息

<a id="opIdedit_16"></a>

## PUT 修改AR 眼镜使用信息

PUT /business/glassUsageLog

修改AR 眼镜使用信息

> Body 请求参数

```json
{
  "createDept": 0,
  "createBy": 0,
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": 0,
  "updateTime": "2019-08-24T14:15:22Z",
  "params": {
    "property1": {},
    "property2": {}
  },
  "id": 0,
  "deviceNo": "string",
  "userId": "string",
  "userName": "string",
  "loginTime": "2019-08-24T14:15:22Z",
  "logoutTime": "2019-08-24T14:15:22Z",
  "totalDuration": 0,
  "stationId": "string",
  "stationName": "string",
  "remark": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|glasses-Authorization|header|string| 否 |none|
|clientid|header|string| 否 |none|
|body|body|[ArGlassUsageLogBo](#schemaarglassusagelogbo)| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RVoid](#schemarvoid)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|string|

<a id="opIdadd_16"></a>

## POST 新增AR 眼镜使用信息

POST /business/glassUsageLog

新增AR 眼镜使用信息

> Body 请求参数

```json
{
  "createDept": 0,
  "createBy": 0,
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": 0,
  "updateTime": "2019-08-24T14:15:22Z",
  "params": {
    "property1": {},
    "property2": {}
  },
  "id": 0,
  "deviceNo": "string",
  "userId": "string",
  "userName": "string",
  "loginTime": "2019-08-24T14:15:22Z",
  "logoutTime": "2019-08-24T14:15:22Z",
  "totalDuration": 0,
  "stationId": "string",
  "stationName": "string",
  "remark": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|glasses-Authorization|header|string| 否 |none|
|clientid|header|string| 否 |none|
|body|body|[ArGlassUsageLogBo](#schemaarglassusagelogbo)| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RVoid](#schemarvoid)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|string|

<a id="opIdexport_16"></a>

## POST 导出AR 眼镜使用信息列表

POST /business/glassUsageLog/export

导出AR 眼镜使用信息列表

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|createDept|query|integer(int64)| 否 |创建部门|
|createBy|query|integer(int64)| 否 |创建者|
|createTime|query|string(date-time)| 否 |创建时间|
|updateBy|query|integer(int64)| 否 |更新者|
|updateTime|query|string(date-time)| 否 |更新时间|
|params|query|object| 否 |请求参数|
|id|query|integer(int64)| 是 |主键id|
|deviceNo|query|string| 是 |设备编号（关联 AR 眼镜表）|
|userId|query|string| 是 |使用人员工号（关联作业人员表）|
|userName|query|string| 是 |使用人员名称|
|loginTime|query|string(date-time)| 是 |登录时间|
|logoutTime|query|string(date-time)| 是 |登出时间|
|totalDuration|query|integer(int64)| 是 |总使用时长（分钟）|
|stationId|query|string| 是 |站点id|
|stationName|query|string| 是 |站点名称|
|remark|query|string| 否 |备注|
|glasses-Authorization|header|string| 否 |none|
|clientid|header|string| 否 |none|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|None|

<a id="opIdgetInfo_16"></a>

## GET 获取AR 眼镜使用信息详细信息

GET /business/glassUsageLog/{id}

获取AR 眼镜使用信息详细信息

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer(int64)| 是 |主键|
|glasses-Authorization|header|string| 否 |none|
|clientid|header|string| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":{"id":0,"deviceNo":"string","userId":"string","userName":"string","loginTime":"2019-08-24T14:15:22Z","logoutTime":"2019-08-24T14:15:22Z","totalDuration":0,"stationId":"string","stationName":"string","remark":"string"}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RArGlassUsageLogVo](#schemararglassusagelogvo)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|string|

<a id="opIdlist_16"></a>

## GET 查询AR 眼镜使用信息列表

GET /business/glassUsageLog/list

查询AR 眼镜使用信息列表

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|createDept|query|integer(int64)| 否 |创建部门|
|createBy|query|integer(int64)| 否 |创建者|
|createTime|query|string(date-time)| 否 |创建时间|
|updateBy|query|integer(int64)| 否 |更新者|
|updateTime|query|string(date-time)| 否 |更新时间|
|params|query|object| 否 |请求参数|
|id|query|integer(int64)| 是 |主键id|
|deviceNo|query|string| 是 |设备编号（关联 AR 眼镜表）|
|userId|query|string| 是 |使用人员工号（关联作业人员表）|
|userName|query|string| 是 |使用人员名称|
|loginTime|query|string(date-time)| 是 |登录时间|
|logoutTime|query|string(date-time)| 是 |登出时间|
|totalDuration|query|integer(int64)| 是 |总使用时长（分钟）|
|stationId|query|string| 是 |站点id|
|stationName|query|string| 是 |站点名称|
|remark|query|string| 否 |备注|
|pageSize|query|integer(int32)| 否 |分页大小|
|pageNum|query|integer(int32)| 否 |当前页数|
|orderByColumn|query|string| 否 |排序列|
|isAsc|query|string| 否 |排序的方向desc或者asc|
|glasses-Authorization|header|string| 否 |none|
|clientid|header|string| 否 |none|

> 返回示例

> 200 Response

```
{"total":0,"rows":[{"id":0,"deviceNo":"string","userId":"string","userName":"string","loginTime":"2019-08-24T14:15:22Z","logoutTime":"2019-08-24T14:15:22Z","totalDuration":0,"stationId":"string","stationName":"string","remark":"string"}],"code":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[TableDataInfoArGlassUsageLogVo](#schematabledatainfoarglassusagelogvo)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|string|

<a id="opIdremove_16"></a>

## DELETE 删除AR 眼镜使用信息

DELETE /business/glassUsageLog/{ids}

删除AR 眼镜使用信息

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|ids|path|array[integer]| 是 |主键串|
|glasses-Authorization|header|string| 否 |none|
|clientid|header|string| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":{}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RVoid](#schemarvoid)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|string|

# 数据模型

<h2 id="tocS_RVoid">RVoid</h2>

<a id="schemarvoid"></a>
<a id="schema_RVoid"></a>
<a id="tocSrvoid"></a>
<a id="tocsrvoid"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {}
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|msg|string|false|none||none|
|data|object|false|none||none|

<h2 id="tocS_ArGlassUsageLogBo">ArGlassUsageLogBo</h2>

<a id="schemaarglassusagelogbo"></a>
<a id="schema_ArGlassUsageLogBo"></a>
<a id="tocSarglassusagelogbo"></a>
<a id="tocsarglassusagelogbo"></a>

```json
{
  "createDept": 0,
  "createBy": 0,
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": 0,
  "updateTime": "2019-08-24T14:15:22Z",
  "params": {
    "property1": {},
    "property2": {}
  },
  "id": 0,
  "deviceNo": "string",
  "userId": "string",
  "userName": "string",
  "loginTime": "2019-08-24T14:15:22Z",
  "logoutTime": "2019-08-24T14:15:22Z",
  "totalDuration": 0,
  "stationId": "string",
  "stationName": "string",
  "remark": "string"
}

```

AR 眼镜使用信息业务对象 ar_glass_usage_log

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createDept|integer(int64)|false|none||创建部门|
|createBy|integer(int64)|false|none||创建者|
|createTime|string(date-time)|false|none||创建时间|
|updateBy|integer(int64)|false|none||更新者|
|updateTime|string(date-time)|false|none||更新时间|
|params|object|false|none||请求参数|
|» **additionalProperties**|object|false|none||none|
|id|integer(int64)|true|none||主键id|
|deviceNo|string|true|none||设备编号（关联 AR 眼镜表）|
|userId|string|true|none||使用人员工号（关联作业人员表）|
|userName|string|true|none||使用人员名称|
|loginTime|string(date-time)|true|none||登录时间|
|logoutTime|string(date-time)|true|none||登出时间|
|totalDuration|integer(int64)|true|none||总使用时长（分钟）|
|stationId|string|true|none||站点id|
|stationName|string|true|none||站点名称|
|remark|string|false|none||备注|

<h2 id="tocS_RArGlassUsageLogVo">RArGlassUsageLogVo</h2>

<a id="schemararglassusagelogvo"></a>
<a id="schema_RArGlassUsageLogVo"></a>
<a id="tocSrarglassusagelogvo"></a>
<a id="tocsrarglassusagelogvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "id": 0,
    "deviceNo": "string",
    "userId": "string",
    "userName": "string",
    "loginTime": "2019-08-24T14:15:22Z",
    "logoutTime": "2019-08-24T14:15:22Z",
    "totalDuration": 0,
    "stationId": "string",
    "stationName": "string",
    "remark": "string"
  }
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|msg|string|false|none||none|
|data|[ArGlassUsageLogVo](#schemaarglassusagelogvo)|false|none||AR 眼镜使用信息视图对象 ar_glass_usage_log|

<h2 id="tocS_ArGlassUsageLogVo">ArGlassUsageLogVo</h2>

<a id="schemaarglassusagelogvo"></a>
<a id="schema_ArGlassUsageLogVo"></a>
<a id="tocSarglassusagelogvo"></a>
<a id="tocsarglassusagelogvo"></a>

```json
{
  "id": 0,
  "deviceNo": "string",
  "userId": "string",
  "userName": "string",
  "loginTime": "2019-08-24T14:15:22Z",
  "logoutTime": "2019-08-24T14:15:22Z",
  "totalDuration": 0,
  "stationId": "string",
  "stationName": "string",
  "remark": "string"
}

```

AR 眼镜使用信息视图对象 ar_glass_usage_log

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键id|
|deviceNo|string|false|none||设备编号（关联 AR 眼镜表）|
|userId|string|false|none||使用人员工号（关联作业人员表）|
|userName|string|false|none||使用人员名称|
|loginTime|string(date-time)|false|none||登录时间|
|logoutTime|string(date-time)|false|none||登出时间|
|totalDuration|integer(int64)|false|none||总使用时长（分钟）|
|stationId|string|false|none||站点id|
|stationName|string|false|none||站点名称|
|remark|string|false|none||备注|

<h2 id="tocS_TableDataInfoArGlassUsageLogVo">TableDataInfoArGlassUsageLogVo</h2>

<a id="schematabledatainfoarglassusagelogvo"></a>
<a id="schema_TableDataInfoArGlassUsageLogVo"></a>
<a id="tocStabledatainfoarglassusagelogvo"></a>
<a id="tocstabledatainfoarglassusagelogvo"></a>

```json
{
  "total": 0,
  "rows": [
    {
      "id": 0,
      "deviceNo": "string",
      "userId": "string",
      "userName": "string",
      "loginTime": "2019-08-24T14:15:22Z",
      "logoutTime": "2019-08-24T14:15:22Z",
      "totalDuration": 0,
      "stationId": "string",
      "stationName": "string",
      "remark": "string"
    }
  ],
  "code": 0,
  "msg": "string"
}

```

表格分页数据对象

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|integer(int64)|false|none||总记录数|
|rows|[[ArGlassUsageLogVo](#schemaarglassusagelogvo)]|false|none||列表数据|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|

